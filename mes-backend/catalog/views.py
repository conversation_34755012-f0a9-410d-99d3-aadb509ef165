from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from authentication.decorators import requires_permission
from operation.models import ManufacturingEvent
from operation.serializers.event_serializers import ManufacturingEventSummarySerializer
from operation.services.serial_number_service import parse_serial_number
from workflow_config.models import Routing, RoutingProduct
from core.pagination import CustomPageNumberPagination
from .serializers import ProductDetailSerializer, ProductSummarySerializer, ProductTypeSerializer
from .models import Product, ProductType


class ProductPartViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.prefetch_related(
        'components',
        'product_components'
    ).select_related('type_id')
    serializer_class = ProductDetailSerializer

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'serial_number',
                openapi.IN_QUERY,
                description="Serial number of the product",
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'include_bom',
                openapi.IN_QUERY,
                description="Include BOM information in the response",
                type=openapi.TYPE_BOOLEAN,
                default=False
            ),
            openapi.Parameter(
                'bom_status',
                openapi.IN_QUERY,
                description="Filter BOMs by status (only used when include_bom=true)",
                type=openapi.TYPE_STRING,
                enum=['active', 'draft', 'obsolete'],
                default='active'
            )
        ]
    )
    @requires_permission(
        ('event', 'get'),
        # ('catalog', 'get')
    )
    @action(detail=False, methods=['get'])
    def by_serial_number(self, request):
        """
        Retrieve product details by serial number with optional BOM information.

        Query Parameters:
            serial_number: Serial number of the product (required)
            include_bom: Boolean flag to include BOM information (default: false)
            bom_status: Filter BOMs by status (default: 'active')
        """
        serial_number = request.query_params.get('serial_number')
        if not serial_number:
            return Response(
                {'error': 'Serial number is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if BOM information should be included
        include_bom = request.query_params.get('include_bom', '').lower() == 'true'
        bom_status = request.query_params.get('bom_status', 'active')

        try:
            parsed_data = parse_serial_number(serial_number)
            print("___parsed_data___", parsed_data)
            product = self.queryset.get(code=parsed_data['part_code'])
            # Add serial number and BOM flags to context
            context = {
                'serial_number': serial_number,
                'include_bom': include_bom,
                'bom_status': bom_status
            }
            serializer = self.get_serializer(product, context=context)
            return Response(serializer.data)
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Product.DoesNotExist:
            return Response(
                {'error': 'Product not found for the given serial number'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def last_failed_event(self, request):
        """
        Get the most recent failed event for a given serial number
        """
        serial_number = request.query_params.get('serial_number')
        if not serial_number:
            return Response(
                {'error': 'Serial number is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            parsed_data = parse_serial_number(serial_number)
            print(parsed_data)
            product = self.queryset.get(code=parsed_data['part_code'])

            last_failed = ManufacturingEvent.objects.filter(
                serial_number=serial_number,
                inspection_status=False
            ).order_by('-timestamp').first()

            if not last_failed:
                return Response(
                    {'message': 'No failed events found for this serial number'},
                    status=status.HTTP_404_NOT_FOUND
                )

            serializer = ManufacturingEventSummarySerializer(last_failed)
            return Response(serializer.data)
        except Product.DoesNotExist:
            return Response(
                {'error': f'Product with code {parsed_data["part_code"]} not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @requires_permission(('catalog', 'get'))
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        # Apply filters
        id_filter = request.query_params.get('id')
        code_filter = request.query_params.get('code')
        name_search = request.query_params.get('name')

        if id_filter:
            queryset = queryset.filter(id=id_filter)
        if code_filter:
            queryset = queryset.filter(code=code_filter)
        if name_search:
            queryset = queryset.filter(name__icontains=name_search)

        serializer = ProductSummarySerializer(queryset, many=True)
        return Response(serializer.data)

    @requires_permission(('catalog', 'create'))
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'include_bom',
                openapi.IN_QUERY,
                description="Include BOM information in the response",
                type=openapi.TYPE_BOOLEAN,
                default=False
            ),
            openapi.Parameter(
                'bom_status',
                openapi.IN_QUERY,
                description="Filter BOMs by status (only used when include_bom=true)",
                type=openapi.TYPE_STRING,
                enum=['active', 'draft', 'obsolete'],
                default='active'
            )
        ]
    )
    @requires_permission(('catalog', 'get'))
    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a product with optional BOM information.

        Query Parameters:
            include_bom: Boolean flag to include BOM information (default: false)
            bom_status: Filter BOMs by status (default: 'active')
        """
        # Check if BOM information should be included
        include_bom = request.query_params.get('include_bom', '').lower() == 'true'
        bom_status = request.query_params.get('bom_status', 'active')

        # Get the product instance
        instance = self.get_object()

        # Get the serializer with appropriate context
        context = self.get_serializer_context()
        context.update({
            'include_bom': include_bom,
            'bom_status': bom_status
        })

        serializer = self.get_serializer(instance, context=context)
        return Response(serializer.data)

    @requires_permission(('catalog', 'update'))
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @requires_permission(('catalog', 'update'))
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @requires_permission(('catalog', 'update'))
    @action(detail=True, methods=['delete'])
    def routing(self, request, pk=None):
        """
        Remove the routing association for this product
        """
        product = self.get_object()

        # Check if product has a routing
        if not product.routings.exists():
            return Response(
                {"error": "This product does not have an associated routing"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Remove the routing association
        RoutingProduct.objects.filter(product=product).delete()

        return Response(status=status.HTTP_204_NO_CONTENT)

    def perform_update(self, serializer):
        """
        Add additional validation before saving
        """
        # Get the routing_id from request data
        routing_id = self.request.data.get('routing_id')

        if routing_id:
            # Check if the routing exists
            try:
                routing = Routing.objects.get(pk=routing_id)
            except Routing.DoesNotExist:
                raise ValidationError({"routing_id": f"Routing with ID {routing_id} does not exist"})

            # Check if the product already has a different routing
            product = serializer.instance
            current_routing = product.routings.first()

            if current_routing and current_routing.id != routing.id:
                # Check if there are any existing routing executions
                if product.routingexecution_set.exists():
                    raise ValidationError({
                        "routing_id": "Cannot change routing for a product that already has routing executions"
                    })

        serializer.save()

    @requires_permission(('catalog', 'delete'))
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class ProductViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for paginated product list API.
    Provides read-only access to products with pagination support.
    """
    queryset = Product.objects.prefetch_related(
        'routings',
        'components',
        'product_components'
    ).select_related('type_id').order_by('-id')
    serializer_class = ProductSummarySerializer
    pagination_class = CustomPageNumberPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'id',
                openapi.IN_QUERY,
                description="Filter by product ID",
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'code',
                openapi.IN_QUERY,
                description="Filter by product code (exact match)",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'name',
                openapi.IN_QUERY,
                description="Search by product name (partial match)",
                type=openapi.TYPE_STRING,
                required=False
            ),
        ]
    )
    @requires_permission(('catalog', 'get'))
    def list(self, request, *args, **kwargs):
        """
        List products with pagination and filtering support.

        Query Parameters:
            id: Filter by product ID
            code: Filter by product code (exact match)
            name: Search by product name (partial match)
            page: Page number for pagination
            page_size: Number of items per page (max 40)
        """
        queryset = self.get_queryset()

        # Apply filters
        id_filter = request.query_params.get('id')
        code_filter = request.query_params.get('code')
        name_search = request.query_params.get('name')

        if id_filter:
            queryset = queryset.filter(id=id_filter)
        if code_filter:
            queryset = queryset.filter(code=code_filter)
        if name_search:
            queryset = queryset.filter(name__icontains=name_search)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        # Fallback to non-paginated response (shouldn't happen with pagination_class set)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @requires_permission(('catalog', 'get'))
    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a specific product by ID.
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class ProductTypeViewSet(viewsets.ModelViewSet):
    queryset = ProductType.objects.all()
    serializer_class = ProductTypeSerializer
    pagination_class = CustomPageNumberPagination

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'label',
                openapi.IN_QUERY,
                description="Search by product type label (case-insensitive partial match)",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'code',
                openapi.IN_QUERY,
                description="Filter by product type code (exact match)",
                type=openapi.TYPE_STRING,
                required=False
            ),
        ]
    )
    def list(self, request, *args, **kwargs):
        """
        List product types with filtering support.

        Query Parameters:
            label: Search by product type label (case-insensitive partial match)
            code: Filter by product type code (exact match)
        """
        queryset = self.get_queryset()

        # Apply filters
        label_search = request.query_params.get('label')
        code_filter = request.query_params.get('code')

        if label_search:
            queryset = queryset.filter(label__icontains=label_search)
        if code_filter:
            queryset = queryset.filter(code=code_filter)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
